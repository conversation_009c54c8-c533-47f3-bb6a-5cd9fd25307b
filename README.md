# @e7w/easy-model

[![npm version](https://badge.fury.io/js/@e7w%2Feasy-model.svg)](https://badge.fury.io/js/@e7w%2Feasy-model)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

一个简单、轻量级的 React 状态管理库，基于类实例和响应式代理实现。通过简洁的 API 提供强大的状态管理能力，支持深度嵌套对象的响应式更新和自动垃圾回收。

## ✨ 特性

- 🚀 **简单易用**: 基于类的状态管理，无需复杂的配置
- 🔄 **响应式**: 自动追踪状态变化，精确更新组件
- 🎯 **类型安全**: 完整的 TypeScript 支持
- 🌳 **深度响应**: 支持嵌套对象和数组的深度监听
- ♻️ **自动回收**: 智能的内存管理和垃圾回收
- 📦 **轻量级**: 零依赖，体积小巧
- 🔗 **实例共享**: 相同参数的实例自动复用

## 📦 安装

```bash
npm install @e7w/easy-model
```

```bash
yarn add @e7w/easy-model
```

```bash
pnpm add @e7w/easy-model
```

## 🚀 快速开始

### 基础用法

```tsx
import { useModel } from "@e7w/easy-model";

class CounterModel {
  constructor(public name: string) {}

  count = 0;

  increment() {
    this.count++;
  }

  decrement() {
    this.count--;
  }

  reset() {
    this.count = 0;
  }
}

function Counter() {
  const { count, increment, decrement, reset } = useModel(CounterModel, ["counter"]);

  return (
    <div>
      <h2>计数器: {count}</h2>
      <button onClick={increment}>+1</button>
      <button onClick={decrement}>-1</button>
      <button onClick={reset}>重置</button>
    </div>
  );
}

function CounterDisplay() {
  const { count } = useModel(CounterModel, ["counter"]);
  return <span>当前计数: {count}</span>;
}

// 两个组件会自动同步状态
function App() {
  return (
    <div>
      <Counter />
      <CounterDisplay />
    </div>
  );
}
```

### 复杂状态管理

```tsx
import { useModel, useInstance } from "@e7w/easy-model";

class TodoModel {
  constructor(public listId: string) {}

  todos: Array<{ id: string; text: string; completed: boolean }> = [];
  filter: 'all' | 'active' | 'completed' = 'all';

  addTodo(text: string) {
    this.todos.push({
      id: Date.now().toString(),
      text,
      completed: false
    });
  }

  toggleTodo(id: string) {
    const todo = this.todos.find(t => t.id === id);
    if (todo) {
      todo.completed = !todo.completed;
    }
  }

  removeTodo(id: string) {
    this.todos = this.todos.filter(t => t.id !== id);
  }

  setFilter(filter: 'all' | 'active' | 'completed') {
    this.filter = filter;
  }

  get filteredTodos() {
    switch (this.filter) {
      case 'active':
        return this.todos.filter(t => !t.completed);
      case 'completed':
        return this.todos.filter(t => t.completed);
      default:
        return this.todos;
    }
  }

  get stats() {
    return {
      total: this.todos.length,
      active: this.todos.filter(t => !t.completed).length,
      completed: this.todos.filter(t => t.completed).length
    };
  }
}

function TodoApp() {
  const todoModel = useModel(TodoModel, ["main"]);
  const [newTodo, setNewTodo] = useState("");

  return (
    <div>
      <input
        value={newTodo}
        onChange={(e) => setNewTodo(e.target.value)}
        onKeyPress={(e) => {
          if (e.key === 'Enter' && newTodo.trim()) {
            todoModel.addTodo(newTodo.trim());
            setNewTodo("");
          }
        }}
        placeholder="添加新任务..."
      />

      <div>
        <button onClick={() => todoModel.setFilter('all')}>全部</button>
        <button onClick={() => todoModel.setFilter('active')}>进行中</button>
        <button onClick={() => todoModel.setFilter('completed')}>已完成</button>
      </div>

      <ul>
        {todoModel.filteredTodos.map(todo => (
          <li key={todo.id}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => todoModel.toggleTodo(todo.id)}
            />
            <span style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}>
              {todo.text}
            </span>
            <button onClick={() => todoModel.removeTodo(todo.id)}>删除</button>
          </li>
        ))}
      </ul>

      <div>
        总计: {todoModel.stats.total} |
        进行中: {todoModel.stats.active} |
        已完成: {todoModel.stats.completed}
      </div>
    </div>
  );
}
```

## 📚 API 参考

### useModel

主要的 Hook，用于在 React 组件中使用状态模型。

```tsx
function useModel<T>(
  ModelClass: new (...args: any[]) => T,
  args: ConstructorParameters<typeof ModelClass>
): T;

function useModel<T>(
  ModelClass: new (...args: any[]) => T,
  args: ConstructorParameters<typeof ModelClass> | null
): T | Partial<T>;
```

**参数:**
- `ModelClass`: 状态模型的类
- `args`: 构造函数参数，用于标识实例

**返回值:**
- 响应式的模型实例

**示例:**
```tsx
// 创建带参数的实例
const userModel = useModel(UserModel, ["user123"]);

// 创建无参数的实例
const appModel = useModel(AppModel, []);

// 条件创建实例
const model = useModel(SomeModel, condition ? ["param"] : null);
```

### useInstance

直接使用已存在的模型实例。

```tsx
function useInstance<T extends object>(model: T): T;
```

**参数:**
- `model`: 模型实例

**返回值:**
- 响应式的模型实例

**示例:**
```tsx
const model = provide(MyModel)("param");
const reactiveModel = useInstance(model);
```

### provide

创建模型提供者，用于实例管理和复用。

```tsx
function provide<T>(
  ModelClass: new (...args: any[]) => T
): (...args: ConstructorParameters<typeof ModelClass>) => T;
```

**参数:**
- `ModelClass`: 状态模型的类

**返回值:**
- 实例工厂函数

**示例:**
```tsx
class MyModel {
  constructor(public id: string) {}
  value = 0;
}

const MyModelProvider = provide(MyModel);

// 相同参数返回同一实例
const instance1 = MyModelProvider("test");
const instance2 = MyModelProvider("test");
console.log(instance1 === instance2); // true
```

### watch

监听对象属性变化。

```tsx
function watch(
  target: object,
  callback: (path: Array<string | symbol>, oldValue: any, newValue: any) => void
): () => void;
```

**参数:**
- `target`: 要监听的对象
- `callback`: 变化回调函数

**返回值:**
- 取消监听的函数

**示例:**
```tsx
const model = provide(MyModel)("test");

const unwatch = watch(model, (path, oldValue, newValue) => {
  console.log(`属性 ${path.join('.')} 从 ${oldValue} 变为 ${newValue}`);
});

model.value = 100; // 触发回调

// 取消监听
unwatch();
```

### finalizationRegistry

注册实例的垃圾回收回调。

```tsx
function finalizationRegistry(model: object): {
  register(callback: () => void): void;
  unregister(): void;
};
```

**参数:**
- `model`: 模型实例

**返回值:**
- 包含 `register` 和 `unregister` 方法的对象

**示例:**
```tsx
const model = provide(MyModel)("test");
const registry = finalizationRegistry(model);

registry.register(() => {
  console.log("模型实例被回收");
});

// 清理引用，触发垃圾回收
model = null;
```

## 🔧 高级用法

### 嵌套模型

```tsx
class UserModel {
  constructor(public userId: string) {}

  name = "";
  email = "";

  updateProfile(name: string, email: string) {
    this.name = name;
    this.email = email;
  }
}

class PostModel {
  constructor(public postId: string) {}

  title = "";
  content = "";
  authorId = "";

  get author() {
    // 动态获取关联的用户模型
    return provide(UserModel)(this.authorId);
  }

  updatePost(title: string, content: string) {
    this.title = title;
    this.content = content;
  }
}

function PostDetail({ postId }: { postId: string }) {
  const post = useModel(PostModel, [postId]);

  return (
    <div>
      <h1>{post.title}</h1>
      <p>作者: {post.author.name}</p>
      <div>{post.content}</div>
    </div>
  );
}
```

### 条件实例化

```tsx
class ConditionalModel {
  constructor(public id: string) {}
  value = 0;
}

function ConditionalComponent({ shouldLoad, id }: { shouldLoad: boolean; id: string }) {
  // 当 shouldLoad 为 false 时，返回空对象
  const model = useModel(ConditionalModel, shouldLoad ? [id] : null);

  if (!shouldLoad) {
    return <div>未加载</div>;
  }

  return <div>值: {model.value}</div>;
}
```

### 自定义 Hook

```tsx
function useCounter(initialValue = 0) {
  class CounterModel {
    constructor(public initial: number) {}

    count = this.initial;

    increment = () => this.count++;
    decrement = () => this.count--;
    reset = () => this.count = this.initial;
  }

  return useModel(CounterModel, [initialValue]);
}

function MyComponent() {
  const counter = useCounter(10);

  return (
    <div>
      <span>{counter.count}</span>
      <button onClick={counter.increment}>+</button>
      <button onClick={counter.decrement}>-</button>
      <button onClick={counter.reset}>重置</button>
    </div>
  );
}
```

## 🚀 开发和构建

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd easy-model

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm test

# 运行测试覆盖率
pnpm test:coverage
```

### 构建

```bash
# 构建库文件
pnpm build

# 仅构建库
pnpm build:lib

# 仅构建类型定义
pnpm build:dts
```

### 代码质量

```bash
# 代码检查
pnpm lint:scripts

# 代码格式化
pnpm format

# 样式检查
pnpm lint:styles
```

## 🔄 与其他状态管理库的比较

| 特性 | easy-model | Redux | Zustand | Valtio |
|------|------------|-------|---------|--------|
| 学习曲线 | 低 | 高 | 中 | 低 |
| 样板代码 | 少 | 多 | 少 | 少 |
| TypeScript 支持 | 优秀 | 良好 | 良好 | 优秀 |
| 开发者工具 | 无 | 优秀 | 良好 | 良好 |
| 包大小 | 小 | 大 | 小 | 小 |
| 响应式 | 自动 | 手动 | 手动 | 自动 |
| 实例管理 | 自动 | 手动 | 手动 | 手动 |

## 🤔 常见问题

### Q: 为什么选择基于类的设计？

A: 类提供了更好的封装性和类型推导，同时让状态和行为更紧密地结合在一起。这使得代码更容易理解和维护。

### Q: 性能如何？

A: easy-model 使用 Proxy 进行响应式追踪，性能与原生对象访问接近。对于大多数应用场景，性能差异可以忽略不计。

### Q: 支持服务端渲染（SSR）吗？

A: 是的，easy-model 完全支持 SSR。实例在服务端和客户端会分别创建，不会产生冲突。

### Q: 如何调试状态变化？

A: 你可以使用 `watch` 函数来监听状态变化，或者在模型方法中添加 console.log 来追踪状态变化。

```tsx
const model = useModel(MyModel, ["test"]);

// 调试状态变化
watch(model, (path, oldValue, newValue) => {
  console.log(`${path.join('.')} 从 ${oldValue} 变为 ${newValue}`);
});
```

### Q: 可以在 React 之外使用吗？

A: 可以！`provide` 和 `watch` 函数是框架无关的，可以在任何 JavaScript 环境中使用。

```tsx
import { provide, watch } from "@e7w/easy-model";

class MyModel {
  value = 0;
  increment() { this.value++; }
}

const MyProvider = provide(MyModel);
const instance = MyProvider();

watch(instance, () => {
  console.log("状态变化了！");
});

instance.increment(); // 输出: 状态变化了！
```

### Q: 如何处理异步操作？

A: 在模型方法中直接使用 async/await：

```tsx
class DataModel {
  constructor(public id: string) {}

  data: any[] = [];
  loading = false;
  error: string | null = null;

  async loadData() {
    this.loading = true;
    this.error = null;

    try {
      const response = await fetch(`/api/data/${this.id}`);
      this.data = await response.json();
    } catch (error) {
      this.error = error.message;
    } finally {
      this.loading = false;
    }
  }
}
```

## 🛣️ 路线图

- [ ] 开发者工具支持
- [ ] 时间旅行调试
- [ ] 持久化插件
- [ ] 中间件系统
- [ ] 更多示例和模板

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

### 开发流程

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE.md](LICENSE.md) 文件了解详细信息。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

如果你觉得这个项目有用，请给它一个 ⭐️！