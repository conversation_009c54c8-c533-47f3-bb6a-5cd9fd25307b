{"compilerOptions": {"rootDir": ".", "paths": {"@/*": ["./src/*"], "@@/*": ["./*"]}, "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "Node", "strict": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "forceConsistentCasingInFileNames": true, "types": ["vite/client", "node"]}, "include": ["src", "example"], "exclude": ["**/*.test.ts", "node_modules", "test/**", ".history/**"]}