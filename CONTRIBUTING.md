# 贡献指南

感谢你对 @e7w/easy-model 项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 Bug 报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献
- 🧪 测试用例
- 📖 示例代码

## 📋 目录

- [开始之前](#开始之前)
- [开发环境设置](#开发环境设置)
- [提交 Bug 报告](#提交-bug-报告)
- [提交功能请求](#提交功能请求)
- [代码贡献流程](#代码贡献流程)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [文档贡献](#文档贡献)
- [发布流程](#发布流程)

## 🚀 开始之前

### 行为准则

参与此项目即表示你同意遵守我们的行为准则：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 专注于对社区最有利的事情
- 对其他社区成员表现出同理心

### 贡献类型

我们欢迎以下类型的贡献：

1. **Bug 修复** - 修复现有功能中的问题
2. **新功能** - 添加新的功能或改进现有功能
3. **文档** - 改进文档、添加示例或教程
4. **测试** - 增加测试覆盖率或改进测试质量
5. **性能优化** - 提升库的性能
6. **重构** - 改进代码结构和可维护性

## 🛠️ 开发环境设置

### 前置要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- Git

### 设置步骤

1. **Fork 项目**
   ```bash
   # 在 GitHub 上 fork 项目到你的账户
   ```

2. **克隆你的 fork**
   ```bash
   git clone https://github.com/YOUR_USERNAME/easy-model.git
   cd easy-model
   ```

3. **添加上游仓库**
   ```bash
   git remote add upstream https://github.com/ORIGINAL_OWNER/easy-model.git
   ```

4. **安装依赖**
   ```bash
   pnpm install
   ```

5. **验证设置**
   ```bash
   # 运行测试
   pnpm test

   # 启动开发服务器
   pnpm dev

   # 构建项目
   pnpm build
   ```

### 开发脚本

```bash
# 开发模式
pnpm dev                 # 启动开发服务器

# 测试
pnpm test               # 运行测试
pnpm test:coverage      # 运行测试并生成覆盖率报告
pnpm test:watch         # 监听模式运行测试

# 构建
pnpm build              # 构建生产版本
pnpm build:lib          # 仅构建库文件
pnpm build:dts          # 仅构建类型定义

# 代码质量
pnpm lint:scripts       # 检查 TypeScript/JavaScript 代码
pnpm lint:styles        # 检查样式文件
pnpm format             # 格式化所有代码
```

## 🐛 提交 Bug 报告

在提交 Bug 报告之前，请：

1. **搜索现有 issues** - 确保问题尚未被报告
2. **使用最新版本** - 确认问题在最新版本中仍然存在
3. **创建最小复现示例** - 提供能重现问题的最小代码

### Bug 报告模板

```markdown
## Bug 描述
简洁清晰地描述 bug。

## 复现步骤
1. 执行 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 期望行为
清晰简洁地描述你期望发生的事情。

## 实际行为
清晰简洁地描述实际发生的事情。

## 复现示例
```tsx
// 提供最小的复现代码
import { useModel } from '@e7w/easy-model';

class TestModel {
  value = 0;
}

function TestComponent() {
  const model = useModel(TestModel, []);
  // ... 问题代码
}
```

## 环境信息
- OS: [e.g. macOS 13.0]
- Node.js: [e.g. 18.17.0]
- @e7w/easy-model: [e.g. 0.1.4]
- React: [e.g. 18.2.0]
- Browser: [e.g. Chrome 115.0]

## 附加信息
添加任何其他有关问题的上下文信息。
```

## 💡 提交功能请求

### 功能请求模板

```markdown
## 功能描述
清晰简洁地描述你想要的功能。

## 问题背景
这个功能请求是否与某个问题相关？请描述。

## 解决方案
清晰简洁地描述你希望发生的事情。

## 替代方案
清晰简洁地描述你考虑过的任何替代解决方案或功能。

## 使用场景
描述这个功能的具体使用场景。

## 示例代码
```tsx
// 期望的 API 使用方式
const model = useModel(MyModel, [], {
  // 新功能的配置
  newFeature: true
});
```

## 附加信息
添加任何其他有关功能请求的上下文或截图。
```

## 🔄 代码贡献流程

### 1. 准备工作

```bash
# 确保你的 fork 是最新的
git checkout main
git pull upstream main
git push origin main

# 创建新的功能分支
git checkout -b feature/your-feature-name
# 或者修复分支
git checkout -b fix/your-fix-name
```

### 2. 开发

- 编写代码
- 添加或更新测试
- 更新文档（如需要）
- 确保所有测试通过
- 确保代码符合规范

### 3. 提交

```bash
# 添加更改
git add .

# 提交更改（使用规范的提交信息）
git commit -m "feat: add new feature description"

# 推送到你的 fork
git push origin feature/your-feature-name
```

### 4. 创建 Pull Request

1. 在 GitHub 上创建 Pull Request
2. 填写 PR 模板
3. 等待代码审查
4. 根据反馈进行修改

### 提交信息规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type):**
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式化（不影响功能）
- `refactor`: 重构（既不是新功能也不是 Bug 修复）
- `test`: 添加或修改测试
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat: add support for conditional model instantiation

Add ability to pass null as args to useModel hook, which returns
an empty object when condition is not met.

Closes #123
```

## 📏 代码规范

### TypeScript/JavaScript

- 使用 TypeScript 进行开发
- 遵循 ESLint 配置
- 使用 Prettier 进行代码格式化
- 优先使用函数式编程风格
- 添加适当的类型注解

### 命名规范

```tsx
// 类名使用 PascalCase
class UserModel {
  // 属性使用 camelCase
  userName = "";

  // 方法使用 camelCase
  updateUserName(name: string) {
    this.userName = name;
  }
}

// 常量使用 UPPER_SNAKE_CASE
const MAX_RETRY_COUNT = 3;

// 文件名使用 kebab-case
// user-model.ts
// use-model.ts
```

### 代码组织

```tsx
// 1. 导入语句
import { useEffect, useReducer } from "react";
import { watch } from "./observe";
import { provide } from "./provide";

// 2. 类型定义
type ModelConstructor<T> = new (...args: any[]) => T;

// 3. 常量
const emptyObject = Object.create(null);

// 4. 主要功能实现
export function useModel<T extends ModelConstructor<any>>(
  Ctor: T,
  args: ConstructorParameters<T>
): InstanceType<T> {
  // 实现...
}

// 5. 辅助函数
function createEmptyObject() {
  return Object.create(null);
}
```

## 🧪 测试指南

### 测试原则

- **覆盖率目标**: 保持 90% 以上的代码覆盖率
- **测试类型**: 单元测试、集成测试、端到端测试
- **测试驱动**: 优先编写测试，然后实现功能

### 编写测试

#### 单元测试示例

```tsx
// test/provide.test.ts
import { describe, expect, it, vi } from "vitest";
import { provide, watch } from "../src";

describe("provide", () => {
  class TestModel {
    constructor(public id: string) {}
    value = 0;
    increment() { this.value++; }
  }

  it("should create singleton instances for same arguments", () => {
    const TestProvider = provide(TestModel);
    const instance1 = TestProvider("test");
    const instance2 = TestProvider("test");

    expect(instance1).toBe(instance2);
  });

  it("should create different instances for different arguments", () => {
    const TestProvider = provide(TestModel);
    const instance1 = TestProvider("test1");
    const instance2 = TestProvider("test2");

    expect(instance1).not.toBe(instance2);
  });

  it("should trigger watchers on property changes", () => {
    const TestProvider = provide(TestModel);
    const instance = TestProvider("test");

    const mockCallback = vi.fn();
    const unwatch = watch(instance, mockCallback);

    instance.increment();

    expect(mockCallback).toHaveBeenCalledWith(["value"], 0, 1);
    expect(instance.value).toBe(1);

    unwatch();
  });
});
```

#### React 组件测试示例

```tsx
// test/use-model.test.tsx
import { describe, expect, it } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { useModel } from "../src";

class CounterModel {
  constructor(public name: string) {}
  count = 0;
  increment() { this.count++; }
  decrement() { this.count--; }
}

function Counter({ name }: { name: string }) {
  const { count, increment, decrement } = useModel(CounterModel, [name]);

  return (
    <div>
      <span data-testid="count">{count}</span>
      <button data-testid="increment" onClick={increment}>+</button>
      <button data-testid="decrement" onClick={decrement}>-</button>
    </div>
  );
}

describe("useModel Hook", () => {
  it("should render initial state correctly", () => {
    render(<Counter name="test" />);

    expect(screen.getByTestId("count")).toHaveTextContent("0");
  });

  it("should update state when methods are called", () => {
    render(<Counter name="test" />);

    fireEvent.click(screen.getByTestId("increment"));
    expect(screen.getByTestId("count")).toHaveTextContent("1");

    fireEvent.click(screen.getByTestId("decrement"));
    expect(screen.getByTestId("count")).toHaveTextContent("0");
  });

  it("should share state between components with same args", () => {
    render(
      <div>
        <Counter name="shared" />
        <Counter name="shared" />
      </div>
    );

    const counts = screen.getAllByTestId("count");
    const incrementButtons = screen.getAllByTestId("increment");

    expect(counts[0]).toHaveTextContent("0");
    expect(counts[1]).toHaveTextContent("0");

    fireEvent.click(incrementButtons[0]);

    expect(counts[0]).toHaveTextContent("1");
    expect(counts[1]).toHaveTextContent("1");
  });
});
```

### 测试命令

```bash
# 运行所有测试
pnpm test

# 监听模式
pnpm test:watch

# 生成覆盖率报告
pnpm test:coverage

# 运行特定测试文件
pnpm test provide.test.ts

# 运行特定测试用例
pnpm test -t "should create singleton instances"
```

### 测试最佳实践

1. **描述性测试名称**: 使用清晰描述测试目的的名称
2. **AAA 模式**: Arrange（准备）、Act（执行）、Assert（断言）
3. **独立性**: 每个测试应该独立运行
4. **边界测试**: 测试边界条件和错误情况
5. **Mock 使用**: 适当使用 mock 来隔离测试

## 📝 文档贡献

### 文档类型

1. **API 文档** - 函数、类、接口的详细说明
2. **使用指南** - 如何使用库的教程
3. **示例代码** - 实际使用场景的代码示例
4. **最佳实践** - 推荐的使用模式

### 文档规范

#### API 文档格式

```tsx
/**
 * 创建响应式模型实例的 React Hook
 *
 * @template T - 模型类的类型
 * @param ModelClass - 模型类构造函数
 * @param args - 构造函数参数，用于实例标识
 * @returns 响应式的模型实例
 *
 * @example
 * ```tsx
 * class UserModel {
 *   constructor(public id: string) {}
 *   name = "";
 * }
 *
 * function UserProfile({ userId }: { userId: string }) {
 *   const user = useModel(UserModel, [userId]);
 *   return <div>{user.name}</div>;
 * }
 * ```
 */
export function useModel<T extends new (...args: any[]) => InstanceType<T>>(
  ModelClass: T,
  args: ConstructorParameters<T>
): InstanceType<T>;
```

#### 示例代码规范

```tsx
// ✅ 好的示例 - 完整且可运行
import { useModel } from '@e7w/easy-model';

class TodoModel {
  constructor(public listId: string) {}

  todos: Todo[] = [];

  addTodo(text: string) {
    this.todos.push({ id: Date.now(), text, completed: false });
  }
}

function TodoList({ listId }: { listId: string }) {
  const todoModel = useModel(TodoModel, [listId]);

  return (
    <div>
      {todoModel.todos.map(todo => (
        <div key={todo.id}>{todo.text}</div>
      ))}
    </div>
  );
}

// ❌ 避免的示例 - 不完整或无法运行
const model = useModel(SomeModel, []);
model.doSomething(); // 缺少上下文
```

### 文档贡献流程

1. **识别需求** - 找到需要改进的文档
2. **创建分支** - `git checkout -b docs/improve-api-docs`
3. **编写内容** - 遵循文档规范
4. **本地预览** - 确保格式正确
5. **提交 PR** - 包含清晰的改进说明

## 🚀 发布流程

### 版本管理

我们使用 [Semantic Versioning](https://semver.org/)：

- **MAJOR** (1.0.0): 不兼容的 API 变更
- **MINOR** (0.1.0): 向后兼容的功能新增
- **PATCH** (0.0.1): 向后兼容的问题修正

### 发布检查清单

发布前确保：

- [ ] 所有测试通过
- [ ] 代码覆盖率达标
- [ ] 文档已更新
- [ ] CHANGELOG.md 已更新
- [ ] 版本号已更新
- [ ] 构建成功

### 发布命令

```bash
# 更新版本号
npm version patch|minor|major

# 构建项目
pnpm build

# 发布到 npm
npm publish
```

## 🤝 代码审查

### 审查标准

1. **功能性**: 代码是否按预期工作
2. **可读性**: 代码是否易于理解
3. **性能**: 是否有性能问题
4. **安全性**: 是否存在安全隐患
5. **测试**: 是否有足够的测试覆盖
6. **文档**: 是否需要更新文档

### 审查流程

1. **自动检查**: CI/CD 自动运行测试和检查
2. **人工审查**: 至少一名维护者审查代码
3. **反馈处理**: 根据反馈修改代码
4. **最终批准**: 维护者批准并合并

## 📞 获取帮助

如果你在贡献过程中遇到问题，可以通过以下方式获取帮助：

- 💬 **讨论**: 在 GitHub Discussions 中提问
- 🐛 **Issues**: 创建 issue 描述问题

---

再次感谢你的贡献！🎉

```tsx
// 1. 导入语句
import { useEffect, useReducer } from "react";
import { watch } from "./observe";
import { provide } from "./provide";

// 2. 类型定义
type ModelConstructor<T> = new (...args: any[]) => T;

// 3. 常量
const emptyObject = Object.create(null);

// 4. 主要功能实现
export function useModel<T extends ModelConstructor<any>>(
  Ctor: T,
  args: ConstructorParameters<T>
): InstanceType<T> {
  // 实现...
}

// 5. 辅助函数
function createEmptyObject() {
  return Object.create(null);
}
```
